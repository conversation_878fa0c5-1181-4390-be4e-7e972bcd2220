<link rel="stylesheet" href="{{ 'component-list-menu.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-search.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-menu-drawer.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'dark-mode-toggle.css' | asset_url }}" media="print" onload="this.media='all'">
<link
  rel="stylesheet"
  href="{{ 'component-cart-notification.css' | asset_url }}"
  media="print"
  onload="this.media='all'"
>

{%- if settings.predictive_search_enabled -%}
  <link rel="stylesheet" href="{{ 'component-price.css' | asset_url }}" media="print" onload="this.media='all'">
{%- endif -%}

{%- if section.settings.menu_type_desktop == 'mega' -%}
  <link rel="stylesheet" href="{{ 'component-mega-menu.css' | asset_url }}" media="print" onload="this.media='all'">
{%- endif -%}

<style>
  header-drawer {
    justify-self: start;
    margin-left: -1.2rem;
  }

  {%- if section.settings.sticky_header_type == 'reduce-logo-size' -%}
    .scrolled-past-header .header__heading-logo-wrapper {
      width: 75%;
    }
  {%- endif -%}

  {%- if section.settings.menu_type_desktop != "drawer" -%}
    @media screen and (min-width: 990px) {
      header-drawer {
        display: none;
      }
    }
  {%- endif -%}

  .menu-drawer-container {
    display: flex;
  }

  .list-menu {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .list-menu--inline {
    display: inline-flex;
    flex-wrap: wrap;
  }

  summary.list-menu__item {
    padding-right: 2.7rem;
  }

  .list-menu__item {
    display: flex;
    align-items: center;
    line-height: calc(1 + 0.3 / var(--font-body-scale));
  }

  .list-menu__item--link {
    text-decoration: none;
    padding-bottom: 1rem;
    padding-top: 1rem;
    line-height: calc(1 + 0.8 / var(--font-body-scale));
  }

  @media screen and (min-width: 750px) {
    .list-menu__item--link {
      padding-bottom: 0.5rem;
      padding-top: 0.5rem;
    }
  }
</style>

{%- style -%}
  .header {
    padding: {{ section.settings.padding_top | times: 0.5 | round: 0 }}px 3rem {{ section.settings.padding_bottom | times: 0.5 | round: 0 }}px 3rem;
  }

  .section-header {
    position: sticky; /* This is for fixing a Safari z-index issue. PR #2147 */
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-header {
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  @media screen and (min-width: 990px) {
    .header {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<script src="{{ 'cart-notification.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'dark-mode-toggle.js' | asset_url }}" defer="defer"></script>

{%- liquid
  for block in section.blocks
    if block.type == '@app'
      assign has_app_block = true
    endif
  endfor
-%}

{% liquid
  assign header_tag = 'div'

  if section.settings.sticky_header_type != 'none'
    assign header_tag = 'sticky-header'
  endif
%}

<{{ header_tag }}
  {% if header_tag == 'sticky-header' %}
    data-sticky-type="{{ section.settings.sticky_header_type }}"
  {% endif %}
  class="header-wrapper color-{{ section.settings.color_scheme }} gradient{% if section.settings.show_line_separator %} header-wrapper--border-bottom{% endif %}"
>
  {%- liquid
    assign social_links = false
    assign localization_forms = false

    if settings.social_twitter_link != blank or settings.social_facebook_link != blank or settings.social_pinterest_link != blank or settings.social_instagram_link != blank or settings.social_youtube_link != blank or settings.social_vimeo_link != blank or settings.social_tiktok_link != blank or settings.social_tumblr_link != blank or settings.social_snapchat_link != blank
      assign social_links = true
    endif

    if localization.available_countries.size > 1 and section.settings.enable_country_selector or section.settings.enable_language_selector and localization.available_languages.size > 1
      assign localization_forms = true
    endif
  -%}
  <header class="header header--{{ section.settings.logo_position }} header--mobile-{{ section.settings.mobile_logo_position }} page-width{% if section.settings.menu_type_desktop == 'drawer' %} drawer-menu{% endif %}{% if section.settings.menu != blank %} header--has-menu{% endif %}{% if has_app_block %} header--has-app{% endif %}{% if social_links %} header--has-social{% endif %}{% if shop.customer_accounts_enabled %} header--has-account{% endif %}{% if localization_forms %} header--has-localizations{% endif %}">
    {%- liquid
      if section.settings.menu != blank
        render 'header-drawer'
      endif

      if section.settings.logo_position == 'top-center' or section.settings.menu == blank
        render 'header-search', input_id: 'Search-In-Modal-1'
      endif
    -%}

    {%- if section.settings.logo_position != 'middle-center' -%}
      {%- if request.page_type == 'index' -%}
        <h1 class="header__heading">
      {%- endif -%}
      <a href="{{ routes.root_url }}" class="header__heading-link link link--text focus-inset">
        {%- if settings.logo != blank -%}
          <div class="header__heading-logo-wrapper">
            {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
            {%- assign logo_height = settings.logo_width | divided_by: settings.logo.aspect_ratio -%}
            {% capture sizes %}(max-width: {{ settings.logo_width | times: 2 }}px) 50vw, {{ settings.logo_width }}px{% endcapture %}
            {% capture widths %}{{ settings.logo_width }}, {{ settings.logo_width | times: 1.5 | round }}, {{ settings.logo_width | times: 2 }}{% endcapture %}
            {{
              settings.logo
              | image_url: width: 600
              | image_tag:
                class: 'header__heading-logo motion-reduce',
                widths: widths,
                height: logo_height,
                width: settings.logo_width,
                alt: logo_alt,
                sizes: sizes,
                preload: true
            }}
          </div>
        {%- else -%}
          <span class="h2">{{ shop.name }}</span>
        {%- endif -%}
      </a>
      {%- if request.page_type == 'index' -%}
        </h1>
      {%- endif -%}
    {%- endif -%}

    {%- liquid
      if section.settings.menu != blank
        if section.settings.menu_type_desktop == 'dropdown'
          render 'header-dropdown-menu'
        elsif section.settings.menu_type_desktop != 'drawer'
          render 'header-mega-menu'
        endif
      endif
    %}

    {%- if section.settings.logo_position == 'middle-center' -%}
      {%- if request.page_type == 'index' -%}
        <h1 class="header__heading">
      {%- endif -%}
      <a href="{{ routes.root_url }}" class="header__heading-link link link--text focus-inset">
        {%- if settings.logo != blank -%}
          <div class="header__heading-logo-wrapper">
            {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
            {%- assign logo_height = settings.logo_width | divided_by: settings.logo.aspect_ratio -%}
            {% capture sizes %}(min-width: 750px) {{ settings.logo_width }}px, 50vw{% endcapture %}
            {% capture widths %}{{ settings.logo_width }}, {{ settings.logo_width | times: 1.5 | round }}, {{ settings.logo_width | times: 2 }}{% endcapture %}
            {{
              settings.logo
              | image_url: width: 600
              | image_tag:
                class: 'header__heading-logo',
                widths: widths,
                height: logo_height,
                width: settings.logo_width,
                alt: logo_alt,
                sizes: sizes,
                preload: true
            }}
          </div>
        {%- else -%}
          <span class="h2">{{ shop.name }}</span>
        {%- endif -%}
      </a>
      {%- if request.page_type == 'index' -%}
        </h1>
      {%- endif -%}
    {%- endif -%}

    <div class="header__icons{% if section.settings.enable_country_selector or section.settings.enable_language_selector %} header__icons--localization header-localization{% endif %}">
      <div class="desktop-localization-wrapper">
        {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
          <localization-form class="small-hide medium-hide" data-prevent-hide>
            {%- form 'localization', id: 'HeaderCountryForm', class: 'localization-form' -%}
              <div>
                <h2 class="visually-hidden" id="HeaderCountryLabel">{{ 'localization.country_label' | t }}</h2>
                {%- render 'country-localization', localPosition: 'HeaderCountry' -%}
              </div>
            {%- endform -%}
          </localization-form>
        {% endif %}

        {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
          <localization-form class="small-hide medium-hide" data-prevent-hide>
            {%- form 'localization', id: 'HeaderLanguageForm', class: 'localization-form' -%}
              <div>
                <h2 class="visually-hidden" id="HeaderLanguageLabel">{{ 'localization.language_label' | t }}</h2>
                {%- render 'language-localization', localPosition: 'HeaderLanguage' -%}
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%}
      </div>
      {% render 'header-search', input_id: 'Search-In-Modal' %}

      {%- comment -%} Dark Mode Toggle Button {%- endcomment -%}
      <dark-mode-toggle class="header__icon">
        <button class="dark-mode-toggle" type="button" aria-label="Switch to dark mode" title="Switch to dark mode">
          <span class="dark-mode-toggle__icon dark-mode-toggle__sun">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="5"/>
              <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
            </svg>
          </span>
          <span class="dark-mode-toggle__icon dark-mode-toggle__moon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
            </svg>
          </span>
          <span class="visually-hidden">Switch to dark mode</span>
        </button>
      </dark-mode-toggle>

      {%- if shop.customer_accounts_enabled -%}
        <a
          href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}"
          class="header__icon header__icon--account link focus-inset{% if section.settings.menu != blank %} small-hide{% endif %}"
          rel="nofollow"
        >
          {%- if section.settings.enable_customer_avatar -%}
            <account-icon>
              {%- if customer and customer.has_avatar? -%}
                {{ customer | avatar }}
              {%- else -%}
                <span class="svg-wrapper">{{ 'icon-account.svg' | inline_asset_content }}</span>
              {%- endif -%}
            </account-icon>
          {%- else -%}
            <span class="svg-wrapper">{{ 'icon-account.svg' | inline_asset_content }}</span>
          {%- endif -%}
          <span class="visually-hidden">
            {%- liquid
              if customer
                echo 'customer.account_fallback' | t
              else
                echo 'customer.log_in' | t
              endif
            -%}
          </span>
        </a>
      {%- endif -%}

      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when '@app' -%}
            {% render block %}
        {%- endcase -%}
      {%- endfor -%}

      <a href="{{ routes.cart_url }}" class="header__icon header__icon--cart link focus-inset" id="cart-icon-bubble">
        {% if cart == empty %}
          <span class="svg-wrapper">{{ 'icon-cart-empty.svg' | inline_asset_content }}</span>
        {% else %}
          <span class="svg-wrapper">{{ 'icon-cart.svg' | inline_asset_content }}</span>
        {% endif %}
        <span class="visually-hidden">{{ 'templates.cart.cart' | t }}</span>
        {%- if cart != empty -%}
          <div class="cart-count-bubble">
            {%- if cart.item_count < 100 -%}
              <span aria-hidden="true">{{ cart.item_count }}</span>
            {%- endif -%}
            <span class="visually-hidden">{{ 'sections.header.cart_count' | t: count: cart.item_count }}</span>
          </div>
        {%- endif -%}
      </a>
    </div>
  </header>
</{{ header_tag }}>

{%- if settings.cart_type == 'notification' -%}
  {%- render 'cart-notification',
    color_scheme: section.settings.color_scheme,
    desktop_menu_type: section.settings.menu_type_desktop
  -%}
{%- endif -%}

{% javascript %}
  class StickyHeader extends HTMLElement {
    constructor() {
      super();
    }

    connectedCallback() {
      this.header = document.querySelector('.section-header');
      this.headerIsAlwaysSticky =
        this.getAttribute('data-sticky-type') === 'always' ||
        this.getAttribute('data-sticky-type') === 'reduce-logo-size';
      this.headerBounds = {};

      this.setHeaderHeight();

      window.matchMedia('(max-width: 990px)').addEventListener('change', this.setHeaderHeight.bind(this));

      if (this.headerIsAlwaysSticky) {
        this.header.classList.add('shopify-section-header-sticky');
      }

      this.currentScrollTop = 0;
      this.preventReveal = false;
      this.predictiveSearch = this.querySelector('predictive-search');

      this.onScrollHandler = this.onScroll.bind(this);
      this.hideHeaderOnScrollUp = () => (this.preventReveal = true);

      this.addEventListener('preventHeaderReveal', this.hideHeaderOnScrollUp);
      window.addEventListener('scroll', this.onScrollHandler, false);

      this.createObserver();
    }

    setHeaderHeight() {
      document.documentElement.style.setProperty('--header-height', `${this.header.offsetHeight}px`);
    }

    disconnectedCallback() {
      this.removeEventListener('preventHeaderReveal', this.hideHeaderOnScrollUp);
      window.removeEventListener('scroll', this.onScrollHandler);
    }

    createObserver() {
      let observer = new IntersectionObserver((entries, observer) => {
        this.headerBounds = entries[0].intersectionRect;
        observer.disconnect();
      });

      observer.observe(this.header);
    }

    onScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      if (this.predictiveSearch && this.predictiveSearch.isOpen) return;

      if (scrollTop > this.currentScrollTop && scrollTop > this.headerBounds.bottom) {
        this.header.classList.add('scrolled-past-header');
        if (this.preventHide) return;
        requestAnimationFrame(this.hide.bind(this));
      } else if (scrollTop < this.currentScrollTop && scrollTop > this.headerBounds.bottom) {
        this.header.classList.add('scrolled-past-header');
        if (!this.preventReveal) {
          requestAnimationFrame(this.reveal.bind(this));
        } else {
          window.clearTimeout(this.isScrolling);

          this.isScrolling = setTimeout(() => {
            this.preventReveal = false;
          }, 66);

          requestAnimationFrame(this.hide.bind(this));
        }
      } else if (scrollTop <= this.headerBounds.top) {
        this.header.classList.remove('scrolled-past-header');
        requestAnimationFrame(this.reset.bind(this));
      }

      this.currentScrollTop = scrollTop;
    }

    hide() {
      if (this.headerIsAlwaysSticky) return;
      this.header.classList.add('shopify-section-header-hidden', 'shopify-section-header-sticky');
      this.closeMenuDisclosure();
      this.closeSearchModal();
    }

    reveal() {
      if (this.headerIsAlwaysSticky) return;
      this.header.classList.add('shopify-section-header-sticky', 'animate');
      this.header.classList.remove('shopify-section-header-hidden');
    }

    reset() {
      if (this.headerIsAlwaysSticky) return;
      this.header.classList.remove('shopify-section-header-hidden', 'shopify-section-header-sticky', 'animate');
    }

    closeMenuDisclosure() {
      this.disclosures = this.disclosures || this.header.querySelectorAll('header-menu');
      this.disclosures.forEach((disclosure) => disclosure.close());
    }

    closeSearchModal() {
      this.searchModal = this.searchModal || this.header.querySelector('details-modal');
      this.searchModal.close(false);
    }
  }

  customElements.define('sticky-header', StickyHeader);
{% endjavascript %}

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Organization",
    "name": {{ shop.name | json }},
    {% if settings.logo %}
      "logo": {{ settings.logo | image_url: width: 500 | prepend: "https:" | json }},
    {% endif %}
    "sameAs": [
      {{ settings.social_twitter_link | json }},
      {{ settings.social_facebook_link | json }},
      {{ settings.social_pinterest_link | json }},
      {{ settings.social_instagram_link | json }},
      {{ settings.social_tiktok_link | json }},
      {{ settings.social_tumblr_link | json }},
      {{ settings.social_snapchat_link | json }},
      {{ settings.social_youtube_link | json }},
      {{ settings.social_vimeo_link | json }}
    ],
    "url": {{ request.origin | append: page.url | json }}
  }
</script>

{%- if request.page_type == 'index' -%}
  {% assign potential_action_target = request.origin | append: routes.search_url | append: '?q={search_term_string}' %}
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "potentialAction": {
        "@type": "SearchAction",
        "target": {{ potential_action_target | json }},
        "query-input": "required name=search_term_string"
      },
      "url": {{ request.origin | append: page.url | json }}
    }
  </script>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.header.name",
  "class": "section-header",
  "max_blocks": 3,
  "settings": [
    {
      "type": "select",
      "id": "logo_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.header.settings.logo_position.options__2.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.header.settings.logo_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.header.settings.logo_position.options__1.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.header.settings.logo_position.options__4.label"
        }
      ],
      "default": "middle-left",
      "label": "t:sections.header.settings.logo_position.label",
      "info": "t:sections.header.settings.logo_help.content"
    },
    {
      "type": "select",
      "id": "mobile_logo_position",
      "options": [
        {
          "value": "center",
          "label": "t:sections.header.settings.mobile_logo_position.options__1.label"
        },
        {
          "value": "left",
          "label": "t:sections.header.settings.mobile_logo_position.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.header.settings.mobile_logo_position.label"
    },
    {
      "type": "link_list",
      "id": "menu",
      "default": "main-menu",
      "label": "t:sections.header.settings.menu.label"
    },
    {
      "type": "select",
      "id": "menu_type_desktop",
      "options": [
        {
          "value": "dropdown",
          "label": "t:sections.header.settings.menu_type_desktop.options__1.label"
        },
        {
          "value": "mega",
          "label": "t:sections.header.settings.menu_type_desktop.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.header.settings.menu_type_desktop.options__3.label"
        }
      ],
      "default": "dropdown",
      "label": "t:sections.header.settings.menu_type_desktop.label"
    },
    {
      "type": "select",
      "id": "sticky_header_type",
      "options": [
        {
          "value": "none",
          "label": "t:sections.header.settings.sticky_header_type.options__1.label"
        },
        {
          "value": "on-scroll-up",
          "label": "t:sections.header.settings.sticky_header_type.options__2.label"
        },
        {
          "value": "always",
          "label": "t:sections.header.settings.sticky_header_type.options__3.label"
        },
        {
          "value": "reduce-logo-size",
          "label": "t:sections.header.settings.sticky_header_type.options__4.label"
        }
      ],
      "default": "on-scroll-up",
      "label": "t:sections.header.settings.sticky_header_type.label"
    },
    {
      "type": "checkbox",
      "id": "show_line_separator",
      "default": true,
      "label": "t:sections.header.settings.show_line_separator.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__1.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "color_scheme",
      "id": "menu_color_scheme",
      "label": "t:sections.header.settings.menu_color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__utilities.content"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": true,
      "label": "t:sections.header.settings.enable_country_selector.label",
      "info": "t:sections.header.settings.enable_country_selector.info"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": true,
      "label": "t:sections.header.settings.enable_language_selector.label",
      "info": "t:sections.header.settings.enable_language_selector.info"
    },
    {
      "type": "checkbox",
      "id": "enable_customer_avatar",
      "default": true,
      "label": "t:sections.header.settings.enable_customer_avatar.label",
      "info": "t:sections.header.settings.enable_customer_avatar.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.spacing"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.header.settings.margin_bottom.label",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 36,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 36,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 20
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ]
}
{% endschema %}
