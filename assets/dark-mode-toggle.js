/**
 * Dark Mode Toggle Functionality for GripballTR Theme
 * Handles theme switching with localStorage persistence
 */

class DarkModeToggle extends HTMLElement {
  constructor() {
    super();
    this.button = this.querySelector('.dark-mode-toggle');
    this.sunIcon = this.querySelector('.dark-mode-toggle__sun');
    this.moonIcon = this.querySelector('.dark-mode-toggle__moon');
    
    this.init();
  }

  init() {
    // Check for saved theme preference or default to 'light'
    const savedTheme = localStorage.getItem('gripball-theme') || 'light';
    this.setTheme(savedTheme);
    
    // Add click event listener
    this.button.addEventListener('click', () => this.toggleTheme());
    
    // Listen for system theme changes
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => this.handleSystemThemeChange(e));
    }
  }

  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    this.setTheme(newTheme);
    this.saveTheme(newTheme);
    
    // Dispatch custom event for other components
    this.dispatchEvent(new CustomEvent('theme-changed', {
      detail: { theme: newTheme },
      bubbles: true
    }));
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    this.updateButtonState(theme);
    this.updateMetaThemeColor(theme);
  }

  updateButtonState(theme) {
    const isDark = theme === 'dark';
    
    // Update aria-label for accessibility
    const label = isDark ? 'Switch to light mode' : 'Switch to dark mode';
    this.button.setAttribute('aria-label', label);
    
    // Update button title
    this.button.title = label;
    
    // Update visually hidden text
    const hiddenText = this.querySelector('.visually-hidden');
    if (hiddenText) {
      hiddenText.textContent = label;
    }
  }

  updateMetaThemeColor(theme) {
    // Update meta theme-color for mobile browsers
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    
    const color = theme === 'dark' ? '#121212' : '#ffffff';
    metaThemeColor.content = color;
  }

  saveTheme(theme) {
    try {
      localStorage.setItem('gripball-theme', theme);
    } catch (error) {
      console.warn('Could not save theme preference:', error);
    }
  }

  handleSystemThemeChange(e) {
    // Only auto-switch if user hasn't manually set a preference
    const savedTheme = localStorage.getItem('gripball-theme');
    if (!savedTheme) {
      const systemTheme = e.matches ? 'dark' : 'light';
      this.setTheme(systemTheme);
    }
  }
}

// Initialize dark mode functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Register the custom element
  if (!customElements.get('dark-mode-toggle')) {
    customElements.define('dark-mode-toggle', DarkModeToggle);
  }
  
  // Apply saved theme immediately to prevent flash
  const savedTheme = localStorage.getItem('gripball-theme');
  if (savedTheme) {
    document.documentElement.setAttribute('data-theme', savedTheme);
  } else {
    // Check system preference
    const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    const systemTheme = prefersDark ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', systemTheme);
  }
});

// Utility function for other scripts to get current theme
window.getCurrentTheme = function() {
  return document.documentElement.getAttribute('data-theme') || 'light';
};

// Utility function for other scripts to set theme
window.setTheme = function(theme) {
  const toggle = document.querySelector('dark-mode-toggle');
  if (toggle) {
    toggle.setTheme(theme);
    toggle.saveTheme(theme);
  }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DarkModeToggle;
}
