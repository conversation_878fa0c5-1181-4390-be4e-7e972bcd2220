/* Scrolling Announcement Bar Styles - PunchX Style */
.scrolling-announcement {
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  padding: 0.8rem 0;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
  font-size: 1.3rem;
  font-weight: 500;
  letter-spacing: 0.05rem;
}

[data-theme="dark"] .scrolling-announcement {
  background-color: rgb(18, 18, 18);
  color: rgb(255, 255, 255);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.scrolling-announcement__content {
  display: inline-block;
  animation: scroll-left 30s linear infinite;
  padding-left: 100%;
}

.scrolling-announcement__text {
  display: inline-block;
  margin-right: 4rem;
  text-transform: uppercase;
}

.scrolling-announcement__text:after {
  content: "•";
  margin-left: 4rem;
  margin-right: 4rem;
  opacity: 0.6;
}

.scrolling-announcement__text:last-child:after {
  display: none;
}

@keyframes scroll-left {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}

/* Pause animation on hover */
.scrolling-announcement:hover .scrolling-announcement__content {
  animation-play-state: paused;
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .scrolling-announcement {
    font-size: 1.2rem;
    padding: 0.6rem 0;
  }
  
  .scrolling-announcement__content {
    animation-duration: 25s;
  }
  
  .scrolling-announcement__text {
    margin-right: 3rem;
  }
  
  .scrolling-announcement__text:after {
    margin-left: 3rem;
    margin-right: 3rem;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .scrolling-announcement__content {
    animation: none;
    transform: none;
    padding-left: 2rem;
  }
  
  .scrolling-announcement {
    text-align: center;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  .scrolling-announcement {
    background-color: Canvas;
    color: CanvasText;
    border-bottom-color: CanvasText;
  }
}

/* Multiple announcement texts */
.scrolling-announcement__content--multiple {
  animation-duration: 45s;
}

.scrolling-announcement__content--fast {
  animation-duration: 20s;
}

.scrolling-announcement__content--slow {
  animation-duration: 40s;
}

/* Custom styling for different announcement types */
.scrolling-announcement--urgent {
  background-color: rgb(231, 39, 39);
  color: white;
}

[data-theme="dark"] .scrolling-announcement--urgent {
  background-color: rgb(220, 38, 38);
}

.scrolling-announcement--sale {
  background-color: rgb(34, 197, 94);
  color: white;
}

[data-theme="dark"] .scrolling-announcement--sale {
  background-color: rgb(22, 163, 74);
}

.scrolling-announcement--info {
  background-color: rgb(59, 130, 246);
  color: white;
}

[data-theme="dark"] .scrolling-announcement--info {
  background-color: rgb(37, 99, 235);
}
