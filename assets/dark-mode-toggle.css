/* Dark Mode Toggle Button Styles */
.dark-mode-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4.4rem;
  height: 4.4rem;
  background: transparent;
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--dark-mode-transition);
  margin-right: 1rem;
}

.dark-mode-toggle:hover {
  border-color: rgba(var(--color-foreground), 0.4);
  background-color: rgba(var(--color-foreground), 0.05);
}

.dark-mode-toggle:focus {
  outline: var(--focused-base-outline);
  outline-offset: var(--focused-base-outline-offset);
}

.dark-mode-toggle__icon {
  width: 2rem;
  height: 2rem;
  transition: var(--dark-mode-transition);
}

.dark-mode-toggle__icon svg {
  width: 100%;
  height: 100%;
  fill: rgb(var(--color-foreground));
}

/* Icon visibility based on theme */
.dark-mode-toggle__sun {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.dark-mode-toggle__moon {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.3s ease, transform 0.3s ease;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
}

[data-theme="dark"] .dark-mode-toggle__sun {
  opacity: 0;
  transform: scale(0.8);
}

[data-theme="dark"] .dark-mode-toggle__moon {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .dark-mode-toggle {
    width: 4rem;
    height: 4rem;
    margin-right: 0.5rem;
  }
  
  .dark-mode-toggle__icon {
    width: 1.8rem;
    height: 1.8rem;
  }
}

/* Animation for smooth transitions */
.dark-mode-toggle__icon {
  animation-duration: 0.3s;
  animation-timing-function: ease-in-out;
}

@keyframes rotate-sun {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(180deg);
  }
}

@keyframes rotate-moon {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(-180deg);
  }
}

.dark-mode-toggle:hover .dark-mode-toggle__sun {
  animation: rotate-sun 0.3s ease-in-out;
}

.dark-mode-toggle:hover .dark-mode-toggle__moon {
  animation: rotate-moon 0.3s ease-in-out;
}

/* Accessibility improvements */
.dark-mode-toggle .visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* High contrast mode support */
@media (forced-colors: active) {
  .dark-mode-toggle {
    border: 1px solid ButtonText;
  }
  
  .dark-mode-toggle__icon svg {
    fill: ButtonText;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .dark-mode-toggle,
  .dark-mode-toggle__icon,
  .dark-mode-toggle__sun,
  .dark-mode-toggle__moon {
    transition: none;
    animation: none;
  }
}
