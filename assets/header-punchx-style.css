/* PunchX Style Header Layout */
.header--punchx-style {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem 3rem;
  position: relative;
}

/* Left Menu */
.header__menu-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

/* Center Logo */
.header__logo-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.header__logo-center .header__brand-name {
  font-size: 3.2rem;
  font-weight: 700;
  letter-spacing: 0.2rem;
  text-transform: uppercase;
  color: white !important;
  text-decoration: none;
  transition: var(--dark-mode-transition);
}

.header__logo-center .header__brand-name:hover {
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
}

/* Right Menu */
.header__menu-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}

/* Menu Items Styling */
.header__menu-item--punchx {
  color: white !important;
  text-decoration: none;
  font-size: 1.4rem;
  font-weight: 500;
  letter-spacing: 0.05rem;
  padding: 0.8rem 1.5rem;
  transition: var(--dark-mode-transition);
  border-radius: 0.4rem;
}

.header__menu-item--punchx:hover {
  color: rgba(255, 255, 255, 0.8) !important;
  background-color: rgba(255, 255, 255, 0.1);
}

.header__menu-item--punchx.header__active-menu-item {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.15);
}

/* Menu List Styling */
.list-menu--punchx {
  display: flex;
  gap: 0.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.list-menu--punchx li {
  display: flex;
}

/* Icons Styling */
.header__icons--punchx {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header__icon--punchx {
  color: white !important;
  transition: var(--dark-mode-transition);
}

.header__icon--punchx:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

.header__icon--punchx svg {
  fill: white !important;
}



/* Desktop Menu Visibility */
@media screen and (min-width: 990px) {
  .header__menu-left .header__inline-menu,
  .header__menu-right .header__inline-menu {
    display: block;
  }

  /* Hide hamburger menu on desktop */
  header-drawer {
    display: none;
  }
}

/* Responsive Design */
@media screen and (max-width: 989px) {
  .header--punchx-style {
    grid-template-columns: auto 1fr auto;
    gap: 1rem;
    padding: 1rem 2rem;
  }

  .header__logo-center .header__brand-name {
    font-size: 2.4rem;
    letter-spacing: 0.1rem;
  }

  .header__menu-left .header__inline-menu,
  .header__menu-right .header__inline-menu {
    display: none;
  }

  /* Show hamburger menu on mobile */
  header-drawer {
    display: block;
  }
}

@media screen and (max-width: 749px) {
  .header--punchx-style {
    padding: 0.8rem 1.5rem;
  }
  
  .header__logo-center .header__brand-name {
    font-size: 2rem;
    letter-spacing: 0.05rem;
  }
  
  .header__icons--punchx {
    gap: 0.5rem;
  }
}

/* Menu Drawer Styling for Mobile - Always Active */
.menu-drawer {
  background-color: rgb(18, 18, 18);
}

.menu-drawer__navigation .list-menu__item a {
  color: white;
}

.menu-drawer__navigation .list-menu__item a:hover {
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Search Modal Styling - Always Active */
.search-modal {
  background-color: rgb(18, 18, 18);
  border-color: rgba(255, 255, 255, 0.2);
}

.search-modal__form input {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

/* Cart Count Bubble */
.cart-count-bubble--punchx {
  background-color: white !important;
  color: rgb(18, 18, 18) !important;
}

/* Accessibility improvements */
.header__menu-item--punchx:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

.header__icon--punchx:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}
