{% comment %}
  Scrolling Announcement Bar - PunchX Style
  
  Usage:
  {% render 'scrolling-announcement', 
    texts: 'Text 1|Text 2|Text 3',
    speed: 'normal',
    type: 'default'
  %}
{% endcomment %}

{{ 'scrolling-announcement.css' | asset_url | stylesheet_tag }}

{%- liquid
  assign announcement_texts = texts | default: 'ÜCRETSİZ KARGO İMKANI|TEMMUZ AYINA ÖZEL İNDİRİM!!!|ÜCRETSİZ KARGO İMKANI|TEMMUZ AYINA ÖZEL İNDİRİM!!!'
  assign speed_class = ''
  assign type_class = ''
  
  case speed
    when 'fast'
      assign speed_class = 'scrolling-announcement__content--fast'
    when 'slow'
      assign speed_class = 'scrolling-announcement__content--slow'
    when 'multiple'
      assign speed_class = 'scrolling-announcement__content--multiple'
    else
      assign speed_class = ''
  endcase
  
  case type
    when 'urgent'
      assign type_class = 'scrolling-announcement--urgent'
    when 'sale'
      assign type_class = 'scrolling-announcement--sale'
    when 'info'
      assign type_class = 'scrolling-announcement--info'
    else
      assign type_class = ''
  endcase
  
  assign text_array = announcement_texts | split: '|'
-%}

<div class="scrolling-announcement {{ type_class }}" role="banner" aria-label="Announcement">
  <div class="scrolling-announcement__content {{ speed_class }}">
    {%- for text in text_array -%}
      <span class="scrolling-announcement__text">{{ text | escape }}</span>
    {%- endfor -%}
    {%- comment -%} Duplicate for seamless loop {%- endcomment -%}
    {%- for text in text_array -%}
      <span class="scrolling-announcement__text">{{ text | escape }}</span>
    {%- endfor -%}
  </div>
</div>

<script>
  // Ensure smooth scrolling and handle visibility changes
  document.addEventListener('DOMContentLoaded', function() {
    const announcements = document.querySelectorAll('.scrolling-announcement');
    
    announcements.forEach(function(announcement) {
      const content = announcement.querySelector('.scrolling-announcement__content');
      
      // Pause animation when page is not visible
      document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
          content.style.animationPlayState = 'paused';
        } else {
          content.style.animationPlayState = 'running';
        }
      });
      
      // Handle reduced motion preference
      if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        content.style.animation = 'none';
        content.style.transform = 'none';
        content.style.paddingLeft = '2rem';
        announcement.style.textAlign = 'center';
      }
    });
  });
</script>
